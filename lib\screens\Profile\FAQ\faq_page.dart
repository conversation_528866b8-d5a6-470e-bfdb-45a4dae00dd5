import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'HELP&SUPPORT/support_center_page.dart' as support;
import 'package:shimmer/shimmer.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../utils/app_themes.dart';

/// Simple data class for each FAQ category
class FAQCategory {
  final String title;
  final IconData icon;
  final List<String> questions;
  final List<String> answers;

  FAQCategory({
    required this.title,
    required this.icon,
    required this.questions,
    required this.answers,
  });
}

class FAQPage extends StatefulWidget {
  const FAQPage({super.key});

  @override
  State<FAQPage> createState() => _FAQPageState();
}

class _FAQPageState extends State<FAQPage> with TickerProviderStateMixin {
  late AnimationController _searchBarController;
  late AnimationController _listAnimationController;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();

    _searchBarController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _listAnimationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _searchBarController.forward();
    _listAnimationController.forward();

    // Clear search when page initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _searchController.clear();
        setState(() {
          _searchQuery = '';
        });
      }
    });

    // Simulate loading
    Future.delayed(const Duration(milliseconds: 1200), () {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    });
  }

  // Controller for the search bar
  final TextEditingController _searchController = TextEditingController();

  // Current search query
  String _searchQuery = '';

  // Example FAQ data with answers
  final List<FAQCategory> faqData = [
    FAQCategory(
      title: 'Charging & Stations',
      icon: Icons.electric_car,
      questions: [
        'How do I start a charging session?',
        'What types of connectors are supported?',
        'How can I find the nearest charging station?',
        'What should I do if a station is not working?',
        'How long does a typical charging session take?',
      ],
      answers: [
        'To start a charging session, locate a station on the map, tap on it, and follow the on-screen instructions. You can pay using the app wallet or RFID card.',
        'We support Type 2, CCS2, CHAdeMO, and GB/T connectors. The app shows which connectors are available at each station.',
        'Open the app and use the map view to see all nearby stations. You can filter by connector type, availability, and distance.',
        'Please report non-working stations through the app by selecting the station and tapping "Report Issue". Our team will address it promptly.',
        'Charging time depends on your vehicle\'s battery capacity and the charger\'s power output. Fast chargers typically take 30-60 minutes for 80% charge.',
      ],
    ),
    FAQCategory(
      title: 'Wallet & Payments',
      icon: Icons.account_balance_wallet_rounded,
      questions: [
        'How do I add money to my wallet?',
        'Why is my transaction pending?',
        'How to check my wallet balance?',
        'What are the wallet transaction limits?',
        'How to link my bank account to the wallet?',
      ],
      answers: [
        'Go to the Wallet section, tap "Add Money", select your payment method, enter the amount, and confirm the transaction.',
        'Transactions may be pending due to network issues or bank verification. Most transactions complete within 30 minutes. If it takes longer, contact support.',
        'Your wallet balance is displayed on the Wallet screen. You can also see it on your profile page.',
        'The minimum transaction is ₹100 and the maximum is ₹10,000 per day. Monthly limit is ₹50,000.',
        'Go to Wallet > Payment Methods > Add Bank Account and follow the instructions to securely link your account.',
      ],
    ),
    FAQCategory(
      title: 'RFID Cards',
      icon: Icons.credit_card,
      questions: [
        'How do I order an RFID card?',
        'How to link my RFID card to my account?',
        'What if I lose my RFID card?',
        'Can I share my RFID card with family members?',
        'How long does RFID card delivery take?',
      ],
      answers: [
        'Go to Profile > RFID Cards > Order New Card. Fill in your delivery details and complete the payment.',
        'Go to Profile > RFID Cards > Link Card. Enter the card number found on the back of your card and follow the instructions.',
        'If you lose your card, immediately go to Profile > RFID Cards, select the lost card, and tap "Deactivate Card" to prevent unauthorized use.',
        'Yes, you can share your RFID card with family members, but all charges will be billed to your account.',
        'RFID cards are typically delivered within 3-5 business days to metro cities and 5-7 days to other locations.',
      ],
    ),
    FAQCategory(
      title: 'Account & Profile',
      icon: Icons.person,
      questions: [
        'How do I update my profile information?',
        'How to change my password?',
        'Can I have multiple vehicles in my profile?',
        'How to delete my account?',
        'How is my data protected?',
      ],
      answers: [
        'Go to Profile > Edit Profile to update your personal information, including name, email, and phone number.',
        'Go to Profile > Security > Change Password. You\'ll need to enter your current password and then set a new one.',
        'Yes, you can add multiple vehicles. Go to Profile > My Vehicles > Add Vehicle and enter the required details.',
        'Go to Profile > Settings > Delete Account. Note that this action is permanent and will erase all your data.',
        'We use industry-standard encryption to protect your personal and payment information. Read our Privacy Policy for more details.',
      ],
    ),
  ];

  // Keep track of which FAQ cards are expanded
  final Set<int> _expandedIndexes = {};
  final Map<String, bool> _expandedQuestions = {};

  // Define the corner radius and colors used in the UI
  final double _cornerRadius = 16.0;
  // Use blue color for FAQ page
  Color get _primaryColor => AppThemes.secondaryColor; // Blue #3D7AF5
  Color get _secondaryColor => const Color(0xFFFF4081); // Pink accent

  @override
  void dispose() {
    _searchBarController.dispose();
    _listAnimationController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  Widget _buildLoadingShimmer() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: 5,
      itemBuilder: (context, index) {
        return Shimmer.fromColors(
          baseColor: isDarkMode ? Colors.grey[800]! : Colors.grey[300]!,
          highlightColor: isDarkMode ? Colors.grey[700]! : Colors.grey[100]!,
          child: Container(
            margin: const EdgeInsets.only(bottom: 16),
            height: 80,
            decoration: BoxDecoration(
              color: isDarkMode ? Colors.grey[850]! : Colors.white,
              borderRadius: BorderRadius.circular(_cornerRadius),
              boxShadow: [
                BoxShadow(
                  color: isDarkMode
                      ? Colors.black.withOpacity(0.3)
                      : Colors.grey.withOpacity(0.2),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
          )
              .animate(delay: Duration(milliseconds: 50 * index))
              .fadeIn(duration: const Duration(milliseconds: 300))
              .move(
                  delay: const Duration(milliseconds: 100),
                  begin: const Offset(20, 0),
                  end: const Offset(0, 0)),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    // Filter the FAQ data based on the search query
    final filteredFaq = faqData.where((category) {
      final titleMatches =
          category.title.toLowerCase().contains(_searchQuery.toLowerCase());
      final questionMatches = category.questions.any(
        (q) => q.toLowerCase().contains(_searchQuery.toLowerCase()),
      );
      return titleMatches || questionMatches;
    }).toList();

    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: isDarkMode ? Colors.black : const Color(0xFFF5F5F5),
      appBar: AppBar(
        backgroundColor: _primaryColor,
        iconTheme:
            const IconThemeData(color: Colors.white), // White back button
        title: const Text(
          'Help & Support',
          style: TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              letterSpacing: 0.5),
        )
            .animate()
            .fadeIn(duration: const Duration(milliseconds: 400))
            .slide(begin: const Offset(0, -0.2), end: const Offset(0, 0)),
        centerTitle: true,
        elevation: 0,
        scrolledUnderElevation: 4,
        actions: [
          Container(
            margin: const EdgeInsets.only(right: 12),
            child: InkWell(
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                      builder: (context) => const support.HelpSupportPage()),
                );
              },
              child: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white,
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black26,
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Icon(
                  Icons.support_agent,
                  color: _primaryColor,
                  size: 24,
                ),
              )
                  .animate()
                  .scale(
                      begin: const Offset(0.8, 0.8),
                      end: const Offset(1.0, 1.0),
                      duration: const Duration(milliseconds: 300),
                      curve: Curves.elasticOut)
                  .fadeIn(duration: const Duration(milliseconds: 300)),
            ),
          ),
        ],
      ),
      body: SafeArea(
        child: Column(
          children: [
            // Search bar with simplified design (no double layer)
            Padding(
              padding: const EdgeInsets.fromLTRB(20, 20, 20, 20),
              child: TextField(
                controller: _searchController,
                onChanged: (value) => setState(() => _searchQuery = value),
                style: TextStyle(
                  color: isDarkMode ? Colors.white : Colors.black,
                  fontSize: 15,
                ),
                decoration: InputDecoration(
                  filled: true,
                  fillColor:
                      isDarkMode ? const Color(0xFF2A2A2A) : Colors.white,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(30),
                    borderSide: BorderSide.none,
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(30),
                    borderSide: BorderSide(color: Colors.grey.withAlpha(77)),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(30),
                    borderSide: BorderSide(color: _primaryColor),
                  ),
                  hintText: 'Search for queries...',
                  hintStyle: TextStyle(
                    color: isDarkMode
                        ? Colors.grey.shade500
                        : Colors.grey.shade600,
                    fontSize: 15,
                  ),
                  prefixIcon: Icon(
                    Icons.search,
                    color: _primaryColor,
                    size: 20,
                  ),
                  suffixIcon: _searchQuery.isNotEmpty
                      ? IconButton(
                          icon: Icon(Icons.clear, color: Colors.grey),
                          onPressed: () {
                            setState(() {
                              _searchController.clear();
                              _searchQuery = '';
                            });
                            _searchBarController.reverse().then((_) {
                              _searchBarController.forward();
                            });
                            HapticFeedback.lightImpact();
                          },
                        )
                      : null,
                  constraints: const BoxConstraints(maxHeight: 60),
                ),
              )
                  .animate()
                  .fadeIn(
                      duration: const Duration(milliseconds: 500),
                      curve: Curves.easeIn)
                  .move(
                      delay: const Duration(milliseconds: 100),
                      begin: const Offset(0, -10),
                      end: const Offset(0, 0),
                      curve: Curves.easeOut),
            ),

            // FAQ list
            Expanded(
              child: filteredFaq.isEmpty
                  ? Center(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.search_off,
                                  size: 64,
                                  color: isDarkMode
                                      ? Colors.grey.shade600
                                      : Colors.grey.shade400)
                              .animate()
                              .scale(
                                  duration: const Duration(milliseconds: 300))
                              .fadeIn(
                                  duration: const Duration(milliseconds: 400)),
                          const SizedBox(height: 16),
                          Text(
                            'No results found',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: isDarkMode
                                  ? Colors.grey.shade300
                                  : Colors.grey.shade700,
                            ),
                          )
                              .animate()
                              .fadeIn(
                                  delay: const Duration(milliseconds: 200),
                                  duration: const Duration(milliseconds: 400))
                              .move(
                                  delay: const Duration(milliseconds: 200),
                                  begin: const Offset(0, 20),
                                  end: const Offset(0, 0)),
                          const SizedBox(height: 8),
                          Text(
                            'Try a different search term',
                            style: TextStyle(
                              fontSize: 16,
                              color: isDarkMode
                                  ? Colors.grey.shade400
                                  : Colors.grey.shade600,
                            ),
                          )
                              .animate()
                              .fadeIn(
                                  delay: const Duration(milliseconds: 350),
                                  duration: const Duration(milliseconds: 400))
                              .move(
                                  delay: const Duration(milliseconds: 350),
                                  begin: const Offset(0, 20),
                                  end: const Offset(0, 0)),
                        ],
                      ),
                    )
                  : _isLoading
                      ? _buildLoadingShimmer()
                      : ListView.builder(
                          key: ValueKey<String>(_searchQuery),
                          padding: const EdgeInsets.symmetric(horizontal: 20),
                          itemCount: filteredFaq.length,
                          itemBuilder: (context, index) {
                            final category = filteredFaq[index];
                            final isExpanded = _expandedIndexes.contains(index);
                            return _buildFaqCard(
                              index: index,
                              category: category,
                              isExpanded: isExpanded,
                            )
                                .animate(
                                    delay: Duration(milliseconds: 50 * index))
                                .fadeIn(
                                    duration: const Duration(milliseconds: 400))
                                .move(
                                    begin: const Offset(0, 10),
                                    end: const Offset(0, 0),
                                    duration:
                                        const Duration(milliseconds: 300));
                          },
                        ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build each FAQ card with smooth expand/collapse animation.
  Widget _buildFaqCard({
    required int index,
    required FAQCategory category,
    required bool isExpanded,
  }) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 10, horizontal: 4),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(_cornerRadius),
      ),
      color: isDarkMode ? const Color(0xFF1A1A1A) : Colors.white,
      elevation: 2,
      shadowColor: isDarkMode ? Colors.black : Colors.black38,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // FAQ Header
          GestureDetector(
            onTap: () {
              setState(() {
                if (isExpanded) {
                  _expandedIndexes.remove(index);
                } else {
                  _expandedIndexes.add(index);
                }
              });
            },
            child: Container(
              height: 60,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [_primaryColor, _primaryColor.withOpacity(0.8)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: isExpanded
                    ? BorderRadius.only(
                        topLeft: Radius.circular(_cornerRadius),
                        topRight: Radius.circular(_cornerRadius),
                      )
                    : BorderRadius.circular(_cornerRadius),
                boxShadow: isExpanded
                    ? [
                        BoxShadow(
                          color: _primaryColor.withAlpha(100),
                          blurRadius: 6,
                          offset: const Offset(0, 3),
                        )
                      ]
                    : [],
              ),
              child: Row(
                children: [
                  Icon(
                    category.icon,
                    color: Colors.white,
                    size: 28,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      category.title,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  Icon(
                    isExpanded
                        ? Icons.keyboard_arrow_up
                        : Icons.keyboard_arrow_down,
                    color: Colors.white,
                  ),
                ],
              ),
            ),
          ),
          // FAQ Body (expanded)
          if (isExpanded)
            Container(
              padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
              decoration: BoxDecoration(
                color: isDarkMode ? const Color(0xFF1A1A1A) : Colors.white,
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(12),
                  bottomRight: Radius.circular(12),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: List.generate(category.questions.length, (qIndex) {
                  final question = category.questions[qIndex];
                  final answer = category.answers[qIndex];
                  final questionKey = '${category.title}-$qIndex';
                  final isQuestionExpanded =
                      _expandedQuestions[questionKey] ?? false;

                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      InkWell(
                        onTap: () {
                          setState(() {
                            _expandedQuestions[questionKey] =
                                !isQuestionExpanded;
                          });
                          HapticFeedback.lightImpact();
                        },
                        child: Padding(
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              AnimatedSwitcher(
                                duration: const Duration(milliseconds: 300),
                                transitionBuilder: (Widget child,
                                    Animation<double> animation) {
                                  return ScaleTransition(
                                      scale: animation, child: child);
                                },
                                child: Icon(
                                  isQuestionExpanded
                                      ? Icons.remove_circle
                                      : Icons.add_circle,
                                  key: ValueKey<bool>(isQuestionExpanded),
                                  color: isQuestionExpanded
                                      ? _secondaryColor
                                      : _primaryColor,
                                  size: 20,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  question,
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    height: 1.4,
                                    color: isDarkMode
                                        ? Colors.white
                                        : Colors.black87,
                                    letterSpacing: 0.2,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      AnimatedCrossFade(
                        duration: const Duration(milliseconds: 300),
                        firstChild: const SizedBox.shrink(),
                        secondChild: Container(
                          margin: const EdgeInsets.only(top: 8),
                          padding: const EdgeInsets.only(
                            left: 28,
                            bottom: 12,
                            right: 8,
                          ),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                            color: isDarkMode
                                ? Colors.grey.shade900.withOpacity(0.3)
                                : Colors.grey.shade100.withOpacity(0.7),
                          ),
                          child: Text(
                            answer,
                            style: TextStyle(
                              fontSize: 15,
                              height: 1.5,
                              color: isDarkMode
                                  ? Colors.grey.shade300
                                  : Colors.grey.shade700,
                              letterSpacing: 0.1,
                              wordSpacing: 1.0,
                            ),
                          ),
                        ),
                        crossFadeState: isQuestionExpanded
                            ? CrossFadeState.showSecond
                            : CrossFadeState.showFirst,
                      ),
                      if (qIndex < category.questions.length - 1)
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          child: Divider(
                            color: isDarkMode
                                ? Colors.grey.shade800
                                : Colors.grey.shade200,
                          ),
                        ),
                    ],
                  );
                }),
              ),
            ),
        ],
      ),
    );
  }
}
// Class moved to support_center_page.dart
